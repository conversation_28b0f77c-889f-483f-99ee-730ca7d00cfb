package se.firme.ms.models.service;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.dao.ISolicitudFirmaDao;
import se.firme.ms.datos.models.dto.DocumentoOrdenDTO;
import se.firme.ms.datos.models.dto.FirmaOrdenRequestDTO;
import se.firme.ms.datos.models.dto.FirmanteOrdenDTO;
import se.firme.ms.datos.models.entity.SolicitudFirma;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

@Service
public class SolicitudFirmaService {

    private final ISolicitudFirmaDao solicitudFirmaDao;
    private final Logger logger = Logger.getLogger(SolicitudFirmaService.class.getName());

    @Autowired
    public SolicitudFirmaService(ISolicitudFirmaDao solicitudFirmaDao) {
        this.solicitudFirmaDao = solicitudFirmaDao;
    }

    public void save(SolicitudFirma solicitudFirma) throws FirmaException {
        try {
            solicitudFirmaDao.save(solicitudFirma);
        } catch (Exception e) {
            logger.severe("SolicitudFirma cannot be saved :: " + solicitudFirma + " :: " + e.getMessage());
            throw new FirmaException(e.getMessage());
        }
    }

    public SolicitudFirma findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(long idArchivoFirma, String email) throws FirmaException {
        try {
            return solicitudFirmaDao.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(idArchivoFirma, email);
        } catch (Exception e) {
            logger.severe("SolicitudFirma cannot be get :: {idArchivoFirma=" + idArchivoFirma + ", email='" + email + "'} :: " + e.getMessage());
            throw new FirmaException(e.getMessage());
        }
    }

    public List<SolicitudFirma> findAllByIdArchivoFirma(Long idArchivoFirma) throws FirmaException {
        try {
            return solicitudFirmaDao.findAllByIdArchivoFirma(idArchivoFirma);
        } catch (Exception e) {
            logger.severe("SolicitudFirma cannot be get :: {idArchivoFirma=" + idArchivoFirma + "} :: " + e.getMessage());
            throw new FirmaException(e.getMessage());
        }
    }

    public Long deleteAllByIdArchivoFirma(Long idArchivoFirma) throws FirmaException {
        try {
            return solicitudFirmaDao.deleteAllByIdArchivoFirma(idArchivoFirma);
        } catch (Exception e) {
            logger.severe("SolicitudFirma cannot be deleted :: {idArchivoFirma=" + idArchivoFirma + "} :: " + e.getMessage());
            throw new FirmaException(e.getMessage());
        }
    }

    @Transactional
    public List<SolicitudFirma> crearSolicitudesFirmaDeArchivos(Long idUsuario, String ids, String email, Date fechaVigencia) throws FirmaException {
        int[] idsArchivosFirmas = Arrays.stream(ids.split("-"))
                .filter(s -> !s.isEmpty())
                .mapToInt(Integer::parseInt)
                .filter(i -> i != 0)
                .toArray();
        return crearSolicitudesFirmaDeArchivos(idUsuario, idsArchivosFirmas, email, fechaVigencia);
    }

    @Transactional
    public List<SolicitudFirma> crearSolicitudesFirmaDeArchivos(Long idUsuario, int[] ids, String email, Date fechaVigencia) throws FirmaException {
        List<SolicitudFirma> solicitudes = new ArrayList<>();

        for (int id : ids) {
            SolicitudFirma solicitudFirma = new SolicitudFirma();
            solicitudFirma.setIdArchivoFirma(id);
            solicitudFirma.setIdUsuario(idUsuario);
            solicitudFirma.setEmailFirmante(email);
            solicitudFirma.setFechaVencimiento(fechaVigencia);

            save(solicitudFirma);

            solicitudes.add(solicitudFirma);
        }

        return solicitudes;
    }

    @Transactional
    public List<SolicitudFirma> crearSolicitudesFirmaOrden(FirmaOrdenRequestDTO request) throws FirmaException {
        List<SolicitudFirma> solicitudes = new ArrayList<>();
        
        try {
            // Convertir fecha de vigencia
            Date fechaVigencia = null;
            if (request.getFechaVigencia() != null && !request.getFechaVigencia().isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                fechaVigencia = sdf.parse(request.getFechaVigencia());
            }
            
            // Procesar cada documento
            for (DocumentoOrdenDTO documento : request.getDocumentos()) {
                // Procesar cada firmante para este documento
                for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                    SolicitudFirma solicitudFirma = new SolicitudFirma();
                    solicitudFirma.setIdArchivoFirma(documento.getIdUsuario()); // Aquí debería ser el ID del archivo creado
                    solicitudFirma.setIdUsuario(documento.getIdUsuario());
                    solicitudFirma.setEmailFirmante(firmante.getEmail());
                    solicitudFirma.setFechaVencimiento(fechaVigencia);
                    solicitudFirma.setRolFirmante(firmante.getRol());
                    solicitudFirma.setOrdenFirma(firmante.getOrden());
                    solicitudFirma.setTipoOrdenFirma(request.getTipoOrden());
                    
                    save(solicitudFirma);
                    solicitudes.add(solicitudFirma);
                }
            }
            
            return solicitudes;
        } catch (ParseException e) {
            logger.severe("Error parsing fecha vigencia: " + e.getMessage());
            throw new FirmaException("Error en formato de fecha de vigencia");
        }
    }
    
    public List<SolicitudFirma> findByIdArchivoFirmaAndOrdenActivo(Long idArchivoFirma, int orden) throws FirmaException {
        try {
            // Aquí necesitarías agregar un método en el DAO para buscar por orden
            return solicitudFirmaDao.findByIdArchivoFirmaAndOrdenFirmaAndFirmadoFalse(idArchivoFirma, orden);
        } catch (Exception e) {
            logger.severe("Error finding solicitudes by orden: " + e.getMessage());
            throw new FirmaException("Error buscando solicitudes por orden");
        }
    }
    
    /**
    * Busca solicitudes por archivo y orden específico - MÉTODO CRÍTICO PARA FIRMA SECUENCIAL
    */
    public List<SolicitudFirma> findByIdArchivoFirmaAndOrdenFirma(Long idArchivoFirma, int orden) throws FirmaException {
        try {
            logger.info("🔍 Buscando solicitudes: archivo=" + idArchivoFirma + ", orden=" + orden);
            
            List<SolicitudFirma> solicitudes = solicitudFirmaDao.findByIdArchivoFirmaAndOrdenFirma(idArchivoFirma, orden);
            
            logger.info("📋 Encontradas " + solicitudes.size() + " solicitudes para orden " + orden);
            for (SolicitudFirma sol : solicitudes) {
                logger.info("   - " + sol.getEmailFirmante() + " (firmado: " + sol.isFirmado() + ")");
            }
            
            return solicitudes;
        } catch (Exception e) {
            logger.severe("Error buscando solicitudes por archivo " + idArchivoFirma + " y orden " + orden + ": " + e.getMessage());
            throw new FirmaException("Error buscando solicitudes por orden");
        }
    }
    
    public boolean puedeArchivoSeguirAlSiguienteOrden(Long idArchivoFirma, int ordenActual) throws FirmaException {
        try {
            // Verificar si todas las firmas del orden actual están completas
            List<SolicitudFirma> solicitudesOrdenActual = solicitudFirmaDao.findByIdArchivoFirmaAndOrdenFirma(idArchivoFirma, ordenActual);
            
            if (solicitudesOrdenActual.isEmpty()) {
                return true; // Si no hay solicitudes en este orden, puede continuar
            }
            
            // Verificar que todas las solicitudes del orden actual estén firmadas
            boolean todasFirmadas = solicitudesOrdenActual.stream()
                .allMatch(SolicitudFirma::isFirmado);
                
            return todasFirmadas;
        } catch (Exception e) {
            logger.severe("Error verificando orden de firma: " + e.getMessage());
            throw new FirmaException("Error verificando orden de firma");
        }
    }
}
