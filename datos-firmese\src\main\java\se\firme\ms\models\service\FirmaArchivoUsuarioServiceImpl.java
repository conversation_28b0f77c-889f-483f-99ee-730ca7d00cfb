/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.ms.models.service.interfaz.IFirmaArchivoUsuarioService;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.dao.IFirmaArchivoUsuarioDao;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
@Service
public class FirmaArchivoUsuarioServiceImpl implements IFirmaArchivoUsuarioService {

    @Autowired
    IFirmaArchivoUsuarioDao firmaArchivoUsuarioDao;

    private static final Logger logger = Logger.getLogger(FirmaArchivoUsuarioServiceImpl.class.getName());

    @Override
    @Transactional
    public void guardarFirmaArchivoUsuario(ArchivoFirma archivoFirma, Usuario usuario, boolean subioInicialmente) {
        FirmaArchivoUsuario firma = new FirmaArchivoUsuario();
        firma.setHashArchivo(archivoFirma.getHashArchivo());
        firma.setIdArchivoFirma(archivoFirma);
        firma.setIdUsuario(usuario);
        firma.setSubio(subioInicialmente);

        firmaArchivoUsuarioDao.save(firma);
    }

    @Override
    @Transactional
    public FirmaArchivoUsuario guardarFirmaArchivoUsuario(ArchivoFirma archivoFirma, Usuario usuario,
            boolean subioInicialmente, String hash, String rutaRelativa, String nombreArchivoFirma, String ipAddress,
            String agente) throws FirmaException {
        try {
            List<FirmaArchivoUsuario> firmas = firmaArchivoUsuarioDao
                    .findByIdArchivoFirmaAndIdusuario(archivoFirma.getIdArchivoFirma(), usuario.getIdUsuario());
            if (firmas == null || firmas.isEmpty()) {
                FirmaArchivoUsuario firma = new FirmaArchivoUsuario();
                firma.setHashArchivo(hash);
                firma.setIdArchivoFirma(archivoFirma);
                firma.setIdUsuario(usuario);
                firma.setSubio(subioInicialmente);
                firma.setRutaRelativaArchivo(rutaRelativa);
                firma.setNombreArchivoFirma(nombreArchivoFirma);
                firma.setIp(ipAddress);
                firma.setAgenteNavegador(agente);
                return firmaArchivoUsuarioDao.save(firma);
            }
            return firmas.get(0);
        } catch (Exception e) {
            logger.severe("Error while saving signature on document with id: " + archivoFirma.getIdArchivoFirma() + " :: " + e.getMessage());
            throw new FirmaException("Error al guardar firma de archivo: " + e.getMessage());
        }
    }

    public boolean guardarRegistro(ArchivoFirma archivoFirma, Usuario usuario, boolean subioInicialmente, String hash,
            String rutaRelativa, String nombreArchivoFirma, String ipAddress, String agente) throws FirmaException {
        try {
            Optional<FirmaArchivoUsuario> firmas = firmaArchivoUsuarioDao
                    .consultarFirmaDocumento(usuario.getCorreoElectronico(), archivoFirma.getIdArchivoFirma());
            if (!firmas.isPresent()) {
                FirmaArchivoUsuario firma = new FirmaArchivoUsuario();
                firma.setHashArchivo(hash);
                firma.setIdArchivoFirma(archivoFirma);
                firma.setIdUsuario(usuario);
                firma.setSubio(subioInicialmente);
                firma.setRutaRelativaArchivo(rutaRelativa);
                firma.setNombreArchivoFirma(nombreArchivoFirma);
                firma.setIp(ipAddress);
                firma.setAgenteNavegador(agente);
                firmaArchivoUsuarioDao.save(firma);
                return true;
            }
            //throw new FirmaException("El usuario ya aceptó la firma de usuario");
            return false;
        } catch (Exception e) {
            logger.severe("Error while saving signature on document with id: " + archivoFirma.getIdArchivoFirma() + " :: " + e.getMessage());
            throw new FirmaException("Error al guardar firma de archivo: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public List<FirmaArchivoUsuario> findByIdArchivoFirma(long idArchivoFirma) {
        return firmaArchivoUsuarioDao.findByIdArchivoFirma(idArchivoFirma);
    }

    @Override
    @Transactional
    public List<FirmaArchivoUsuario> findByIdArchivoFirmaAndIdusuario(long idArchivoFirma, long idUsuario) {
        return firmaArchivoUsuarioDao.findByIdArchivoFirmaAndIdusuario(idArchivoFirma, idUsuario);
    }

    @Override
    @Transactional
    public void actualizarFirma(String hashArchivo, String rutaRelativa, String nombreArchivo,
            long idFirmaArchivoUsuario) {
        firmaArchivoUsuarioDao.actualizarFirma(hashArchivo, rutaRelativa, nombreArchivo, idFirmaArchivoUsuario);
    }

    public Optional<FirmaArchivoUsuario> consultarFirmaDocumento(String correoElectronico, long idArchivoFirma) {
        return firmaArchivoUsuarioDao.consultarFirmaDocumento(correoElectronico, idArchivoFirma);
    }

    public int contarfirmantes(long idArchivoFirma) {
        return firmaArchivoUsuarioDao.contarfirmantes(idArchivoFirma);
    }

    public List<FirmaArchivoUsuario> findByIdArchivoFirmaUsuarioPropietario(long idArchivoFirma, Long idUsuario) {
        return firmaArchivoUsuarioDao.findByIdArchivoFirmaUsuarioPropietario(idArchivoFirma);
    }

    public List<FirmaArchivoUsuario> findByUsuario(Long idUsuario) {
        return firmaArchivoUsuarioDao.findByUsuario(idUsuario);
    }

    public List<FirmaArchivoUsuario> firmadosSolicitudPendiente(Integer idArchivo) {
        return firmaArchivoUsuarioDao.firmadosSolicitudPendiente(idArchivo);
    }
    
    // implementación del dao para contar firmas
    @Override
    public int contarFirmasUsuario(Long idUsuario) throws FirmaException {
        try{
            return firmaArchivoUsuarioDao.contarFirmasUsuario(idUsuario);
        } catch (Exception e) {
            throw new FirmaException("Error al contar firmas de usuario: "+e.getMessage());
        }
    }
}
