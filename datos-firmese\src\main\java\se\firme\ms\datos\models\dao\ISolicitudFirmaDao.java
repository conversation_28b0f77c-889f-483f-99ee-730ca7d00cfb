package se.firme.ms.datos.models.dao;

import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.jpa.repository.Query;
import se.firme.ms.datos.models.entity.SolicitudFirma;

import java.util.List;

public interface ISolicitudFirmaDao extends PagingAndSortingRepository<SolicitudFirma, Long> {
    public SolicitudFirma findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(long idArchivoFirma, String email);
    public List<SolicitudFirma> findAllByIdArchivoFirma(long idArchivoFirma);

    public Long deleteAllByIdArchivoFirma(long idArchivoFirma);

    @Query(value = "SELECT * FROM solicitud_firma WHERE id_archivo_firma = ?1 AND orden_firma = ?2 AND firmado = false", nativeQuery = true)
    List<SolicitudFirma> findByIdArchivoFirmaAndOrdenFirmaAndFirmadoFalse(Long idArchivoFirma, int ordenFirma);

    @Query(value = "SELECT * FROM solicitud_firma WHERE id_archivo_firma = ?1 AND orden_firma = ?2", nativeQuery = true)
    List<SolicitudFirma> findByIdArchivoFirmaAndOrdenFirma(Long idArchivoFirma, int ordenFirma);

    @Query(value = "SELECT DISTINCT orden_firma FROM solicitud_firma WHERE id_archivo_firma = ?1 ORDER BY orden_firma", nativeQuery = true)
    List<Integer> findDistinctOrdenByIdArchivoFirma(Long idArchivoFirma);

    @Query(value = "SELECT COUNT(*) FROM solicitud_firma WHERE id_archivo_firma = ?1 AND orden_firma = ?2 AND firmado = true", nativeQuery = true)
    int countFirmadosByIdArchivoFirmaAndOrden(Long idArchivoFirma, int ordenFirma);

    @Query(value = "SELECT COUNT(*) FROM solicitud_firma WHERE id_archivo_firma = ?1 AND orden_firma = ?2", nativeQuery = true)
    int countTotalByIdArchivoFirmaAndOrden(Long idArchivoFirma, int ordenFirma);
}
