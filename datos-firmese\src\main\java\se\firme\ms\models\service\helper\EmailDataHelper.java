package se.firme.ms.models.service.helper;
import java.util.List;
import java.util.Arrays;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import se.firme.commons.firmese.dto.DatosEmailMultipleFirmaDTO;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.interfaz.IArchivoFirmaService;
import se.firme.ms.models.service.interfaz.ITokenService;
import se.firme.ms.models.service.interfaz.IUsuarioService;
import se.firme.ms.datos.models.entity.Token;

@Component
public class EmailDataHelper {

    private static Logger logger = Logger.getLogger(EmailDataHelper.class.getName());

    @Autowired
    private IUsuarioService usuarioService;

    @Autowired
    private  TokenServiceImpl tokenServiceImpl;

    @Autowired
    private IArchivoFirmaService archivoFirmaService;

    /**
     * Valida que todos los datos necesarios para el correo estén disponibles ANTES de iniciar el proceso de firma.
     * Lanza excepción si falta algún dato crítico.
     */
    public void validarDatosParaCorreo(Long idUsuario, String emailFirmante, String nombreFirmante, List<String> nombresDocumentos) throws FirmaException {
        StringBuilder errores = new StringBuilder();

        // 1. Validar nombres de documentos
        if (nombresDocumentos == null || nombresDocumentos.isEmpty()) {
            errores.append("- No hay nombres de documentos disponibles\n");
        } else {
            for (String nombre : nombresDocumentos) {
                if (nombre == null || nombre.trim().isEmpty() || "Documento".equals(nombre.trim())) {
                    errores.append("- Nombre de documento inválido o genérico: ").append(nombre).append("\n");
                    break;
                }
            }
        }

        // 2. Validar datos del remitente
        try {
            Usuario remitente = usuarioService.findById(idUsuario);
            if (remitente == null) {
                errores.append("- No se encontró el usuario remitente con ID: ").append(idUsuario).append("\n");
            } else {
                if (remitente.getNombreCompleto() == null || remitente.getNombreCompleto().trim().isEmpty()) {
                    errores.append("- El remitente no tiene nombre completo\n");
                }
                if (remitente.getCorreoElectronico() == null || remitente.getCorreoElectronico().trim().isEmpty()) {
                    errores.append("- El remitente no tiene email\n");
                }
            }
        } catch (Exception e) {
            errores.append("- Error consultando datos del remitente: ").append(e.getMessage()).append("\n");
        }

        // 3. Validar datos del firmante
        if (emailFirmante == null || emailFirmante.trim().isEmpty()) {
            errores.append("- Email del firmante es requerido\n");
        } else {
            // Validar que el nombre del firmante esté disponible
            String nombreFirmanteReal = nombreFirmante;
            if (nombreFirmanteReal == null || nombreFirmanteReal.trim().isEmpty()) {
                try {
                    Usuario firmante = usuarioService.findByEmail(emailFirmante);
                    if (firmante == null || firmante.getNombreCompleto() == null || firmante.getNombreCompleto().trim().isEmpty()) {
                        errores.append("- No se puede determinar el nombre del firmante para: ").append(emailFirmante).append("\n");
                    }
                } catch (Exception e) {
                    errores.append("- Error consultando datos del firmante: ").append(e.getMessage()).append("\n");
                }
            }
        }

        // Si hay errores, lanzar excepción
        if (errores.length() > 0) {
            String mensajeError = "No se puede iniciar el proceso de firma. Faltan datos críticos para el correo:\n" + errores.toString();
            logger.severe(mensajeError);
            throw new FirmaException(mensajeError);
        }

        logger.info("✅ Validación de datos para correo exitosa");
    }
    
    // USADO EN: TokenGenerationService, FirmaOrdenService
    public DatosEmailMultipleFirmaDTO extraerDatos(Long idUsuario, String emailFirmante,
            String nombreFirmante, List<String> nombresDocumentos) throws FirmaException {

        try{
            Usuario remitente = usuarioService.findById(idUsuario);
            String nombreRemitente = remitente.getNombreCompleto();

        } catch(Exception e){
            logger.severe("Error en validación previa a extracción de datos: " + e.getMessage());
            throw new FirmaException("No se pueden extraer los datos necesarios para el correo: " + e.getMessage());
        }
        
        try {
        	logger.info("El nombre llegó como: "+nombresDocumentos.get(0));
            // Obtener datos del remitente
            Usuario remitente = usuarioService.findById(idUsuario);
            String nombreRemitente = remitente != null ? remitente.getNombreCompleto() : "Usuario";
            String emailRemitente = remitente != null ? remitente.getCorreoElectronico() : "";
            
            // Usar el nombre del firmante si está disponible, sino intentar obtenerlo
            String nombreFirmanteReal = nombreFirmante != null && !nombreFirmante.trim().isEmpty() ? 
                    nombreFirmante : obtenerNombreFirmante(emailFirmante);
            
            // Crear y retornar DTO
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos != null ? nombresDocumentos : Arrays.asList("Documento"));
            datos.setNombreFirmante(nombreFirmanteReal);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);

            logger.info("Datos extraídos exitosamente - Remitente: " + nombreRemitente + ", Firmante: " + nombreFirmanteReal + 
            		"nombre documentos:" + nombresDocumentos.get(0));
            
            return datos;
                
        } catch (Exception e) {
            logger.severe("Error extrayendo datos: " + e.getMessage());
            throw new FirmaException("No se pueden extraer los datos necesarios para el correo: " + e.getMessage());
        }
    }

        // Método para el reenvio buscando los datos a traves del token, FirmaNegocio.reenviarSolicitudFirma
        public DatosEmailMultipleFirmaDTO extraerDatosDesdeToken(Token token) throws FirmaException {
            logger.info("Extrayendo datos del dto desde el token");
        try {
            
            // 1. DATOS DEL REMITENTE (desde token.getIdUsuario())
            Usuario remitente = token.getIdUsuario();
            String nombreRemitente = remitente != null ? remitente.getNombreCompleto() : "Usuario";
            String emailRemitente = remitente != null ? remitente.getCorreoElectronico() : "";
            
            // 2. DATOS DEL FIRMANTE (desde token.getEmailFirmante())
            String nombreFirmante = obtenerNombreFirmante(token.getEmailFirmante());
            
            // 3. NOMBRES REALES DE DOCUMENTOS (desde token.getIds())
            List<String> nombresDocumentos = obtenerNombresRealesDocumentos(token.getIds());
            
            // 4. CREAR DTO COMPLETO
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmante);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);
            
            logger.info(" Datos extraídos desde token:");
            logger.info("  Remitente: " + nombreRemitente + " (" + emailRemitente + ")");
            logger.info("  Firmante: " + nombreFirmante + " (" + token.getEmailFirmante() + ")");
            logger.info("  Documentos: " + nombresDocumentos);
            
            return datos;
                
        } catch (Exception e) {
            logger.severe("Error extrayendo datos desde token: " + e.getMessage());
            throw new FirmaException("No se pueden extraer los datos necesarios para el correo desde el token: " + e.getMessage());
        }
    }
    
    private String obtenerNombreFirmante(String email) {
        try {
            Usuario firmante = usuarioService.findByEmail(email);
            return firmante != null ? firmante.getNombreCompleto() : "Estimado usuario";
        } catch (Exception e) {
            return "Estimado usuario";
        }
    }
    // Obtener nombres de los documentos a partir del string ids 0-
    private List<String> obtenerNombresRealesDocumentos(String ids) {
        try {
            if (ids != null && !ids.trim().isEmpty()) {
                logger.info("Obteniendo nombres de los archivos: " + ids);
                
                //  quitar "0-" del inicio y "-0" del final
                String idsLimpios = ids;
                if (idsLimpios.startsWith("0-")) {
                    idsLimpios = idsLimpios.substring(2);
                }
                if (idsLimpios.endsWith("-0")) {
                    idsLimpios = idsLimpios.substring(0, idsLimpios.length() - 2);
                }
                // Usar el servicio de archivoFirma para encontrar los nombres
                List<ArchivoFirma> archivos = archivoFirmaService.buscarArchivosFirma(idsLimpios);
                
                if (archivos != null && !archivos.isEmpty()) {
                    List<String> nombresReales = archivos.stream()
                            .map(ArchivoFirma::getNombreArchivo)
                            .collect(Collectors.toList());
                    
                    logger.info("Nombres de los archivos encontrados: " + nombresReales);
                    return nombresReales;
                }
            }
        } catch (Exception e) {
            logger.warning("Error obteniendo nombres reales: " + e.getMessage());
        }
        
        return obtenerNombresGenericosDesdeIds(ids); // fallback para sacar nombres genéricos
    }
    
    // Método utilizado para sacar datos en el flujo enviarNotificacionSolicitud de emailNotificationService
    // EmailNotificationService.enviarNotificacionSolicitud y enviarNotificacionSolicitudSinAdjuntos
    // NOTA: Este método mantiene fallback porque se usa en notificaciones donde el proceso ya está avanzado
    public DatosEmailMultipleFirmaDTO extraerDatosOptimizado(String email, String nombreArchivo, String tokenString) {
        logger.info("Extrayendo datos para enviarNotificacionSolicitud");
        
        try {
            List<String> nombresDocumentos = nombreArchivo != null ? 
                    Arrays.asList(nombreArchivo) : Arrays.asList("Documento");
        
            String nombreFirmante = obtenerNombreFirmante(email);
            
            // SOLO CONSULTAR TOKEN PARA DATOS DEL REMITENTE
            String nombreRemitente = "Usuario";
            String emailRemitente = "";
            
            try {
                // obtener los datos desde el token
                Token tokenEntity = tokenServiceImpl.findTokenByID(tokenString);
                if (tokenEntity != null && tokenEntity.getIdUsuario() != null) {
                    Usuario remitente = tokenEntity.getIdUsuario();
                    nombreRemitente = remitente.getNombreCompleto();
                    emailRemitente = remitente.getCorreoElectronico();
                    
                    // Si no tiene nombre de archivo, extraer desde token
                    if (nombreArchivo == null && tokenEntity.getIds() != null) {
                        nombresDocumentos = obtenerNombresRealesDocumentos(tokenEntity.getIds());
                    }
                }
            } catch (Exception e) {
                logger.warning("No se pudieron obtener datos del remitente desde token: " + e.getMessage());
            }
            
            // 4. CREAR DTO
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmante);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);
            
            logger.info("Datos para enviarNotificacionSolicitud extraídos Archivo: " + (nombreArchivo != null ? nombreArchivo : "desde token"));
            return datos;
            
        } catch (Exception e) {
            logger.warning("Error en extracción híbrida, usando fallback completo: " + e.getMessage());
            // Fallback si no se pudo extraer los datos a través del string del token utilizar el token completo
            try {
                Token tokenEntity = tokenServiceImpl.findTokenByID(tokenString);
                return extraerDatosDesdeToken(tokenEntity);
            } catch (Exception fallbackEx) {
                logger.severe("Error en fallback completo: " + fallbackEx.getMessage());
                return crearDatosFallback(Arrays.asList("Documento"));
            }
        }
    }

    // métodos de fallback
    // genera nombres genéricos cuando no se pueden obtener los reales
     
    private List<String> obtenerNombresGenericosDesdeIds(String ids) {
        if (ids != null) {
            // Limpiar formato y crear nombres genéricos
            String idsLimpios = ids.replace("0-", "").replace("-0", "");
            return Arrays.stream(idsLimpios.split("-"))
                    .filter(id -> !id.trim().isEmpty())
                    .map(id -> "Documento " + id)
                    .collect(Collectors.toList());
        }
        return Arrays.asList("Documento");
    }
    
    private DatosEmailMultipleFirmaDTO crearDatosFallback(List<String> nombresDocumentos) {
        DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
        datos.setNombreDocumento(nombresDocumentos != null ? nombresDocumentos : Arrays.asList("Documento"));
        datos.setNombreFirmante("Estimado usuario");
        datos.setNombreRemitente("Usuario");
        datos.setEmailRemitente("");
        return datos;
    }
}